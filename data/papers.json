[{"id": "paper_1752760074271", "title": "A Comprehensive Study of Machine Learning Applications", "authors": ["Unknown Author"], "abstract": "This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.", "content": "A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.", "filePath": "/home/<USER>/project/DocuMancer/new/documancer/uploads/1752760074271_1706.03762v7.pdf", "uploadedAt": "2025-07-17T13:47:54.276Z", "lastAccessedAt": "2025-07-17T13:47:54.276Z", "tags": ["machine learning", "deep learning", "natural language processing", "computer vision"]}, {"id": "paper_1752760143019", "title": "A Comprehensive Study of Machine Learning Applications", "authors": ["Unknown Author"], "abstract": "This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.", "content": "A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.", "filePath": "/home/<USER>/project/DocuMancer/new/documancer/uploads/1752760143019_2005.12872v3.pdf", "uploadedAt": "2025-07-17T13:49:03.028Z", "lastAccessedAt": "2025-07-17T13:49:03.028Z", "tags": ["machine learning", "deep learning", "natural language processing", "computer vision"]}, {"id": "paper_1752760647112", "title": "A Comprehensive Study of Machine Learning Applications", "authors": ["Unknown Author"], "abstract": "This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.", "content": "A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.", "filePath": "/api/files/1752760647112_test-paper.pdf", "uploadedAt": "2025-07-17T13:57:27.113Z", "lastAccessedAt": "2025-07-17T13:57:27.113Z", "tags": ["machine learning", "deep learning", "natural language processing", "computer vision"]}, {"id": "paper_1752760745397", "title": "A Comprehensive Study of Machine Learning Applications", "authors": ["Unknown Author"], "abstract": "This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.", "content": "A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.", "filePath": "/api/files/1752760745397_1706.03762v7.pdf", "uploadedAt": "2025-07-17T13:59:05.404Z", "lastAccessedAt": "2025-07-17T13:59:05.404Z", "tags": ["machine learning", "deep learning", "natural language processing", "computer vision"]}, {"id": "paper_1752760799745", "title": "A Comprehensive Study of Machine Learning Applications", "authors": ["Unknown Author"], "abstract": "This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.", "content": "A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.", "filePath": "/api/files/1752760799745_2506.21547v1.pdf", "uploadedAt": "2025-07-17T13:59:59.759Z", "lastAccessedAt": "2025-07-17T13:59:59.759Z", "tags": ["machine learning", "deep learning", "natural language processing", "computer vision"]}, {"id": "paper_1752760827888", "title": "A Comprehensive Study of Machine Learning Applications", "authors": ["Unknown Author"], "abstract": "This paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.", "content": "A Comprehensive Study of Machine Learning Applications\n\nAuthors: <AUTHORS>\nInstitution: University of Technology\nDate: December 2024\n\nAbstract\nThis paper presents a comprehensive analysis of machine learning applications in academic research. We explore various methodologies, examine current trends, and propose future directions for the field.\n\n1. Introduction\nMachine learning has revolutionized numerous fields of study, from natural language processing to computer vision.\n\n2. Methodology\nOur research methodology combines systematic literature review with empirical analysis.\n\n3. Key Findings\n• Increased adoption of deep learning techniques in research\n• Growing emphasis on interpretable AI models\n• Rising importance of ethical considerations in ML research\n\n4. Conclusion\nThis study demonstrates the transformative impact of machine learning on academic research.", "filePath": "/api/files/1752760827888_test-paper.pdf", "uploadedAt": "2025-07-17T14:00:27.889Z", "lastAccessedAt": "2025-07-17T14:00:27.889Z", "tags": ["machine learning", "deep learning", "natural language processing", "computer vision"]}, {"id": "paper_1752761507292", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2537 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752761507292_test-paper.pdf", "uploadedAt": "2025-07-17T14:11:47.594Z", "lastAccessedAt": "2025-07-17T14:11:47.594Z", "tags": []}, {"id": "paper_1752761547625", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752761547625_2506.21547v1.pdf", "uploadedAt": "2025-07-17T14:12:27.641Z", "lastAccessedAt": "2025-07-17T14:12:27.641Z", "tags": []}, {"id": "paper_1752761782999", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752761782999_1706.03762v7.pdf", "uploadedAt": "2025-07-17T14:16:23.003Z", "lastAccessedAt": "2025-07-17T14:16:23.003Z", "tags": []}, {"id": "paper_1752763338226", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752763338226_1706.03762v7.pdf", "uploadedAt": "2025-07-17T14:42:18.231Z", "lastAccessedAt": "2025-07-17T14:42:18.231Z", "tags": []}, {"id": "paper_1752763355733", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2537 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752763355733_test-paper.pdf", "uploadedAt": "2025-07-17T14:42:35.735Z", "lastAccessedAt": "2025-07-17T14:42:35.735Z", "tags": []}, {"id": "paper_1752763359543", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752763359543_2506.21547v1.pdf", "uploadedAt": "2025-07-17T14:42:39.555Z", "lastAccessedAt": "2025-07-17T14:42:39.555Z", "tags": []}, {"id": "paper_1752763418722", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752763418722_2506.21547v1.pdf", "uploadedAt": "2025-07-17T14:43:38.737Z", "lastAccessedAt": "2025-07-17T14:43:38.737Z", "tags": []}, {"id": "paper_1752763937496", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 7634041 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752763937496_2005.12872v3.pdf", "uploadedAt": "2025-07-17T14:52:17.848Z", "lastAccessedAt": "2025-07-17T14:52:17.848Z", "tags": []}, {"id": "paper_1752763977339", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752763977339_1706.03762v7.pdf", "uploadedAt": "2025-07-17T14:52:57.343Z", "lastAccessedAt": "2025-07-17T14:52:57.343Z", "tags": []}, {"id": "paper_1752764044168", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764044168_1706.03762v7.pdf", "uploadedAt": "2025-07-17T14:54:04.173Z", "lastAccessedAt": "2025-07-17T14:54:04.173Z", "tags": []}, {"id": "paper_1752764059830", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2537 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764059830_test-paper.pdf", "uploadedAt": "2025-07-17T14:54:19.832Z", "lastAccessedAt": "2025-07-17T14:54:19.832Z", "tags": []}, {"id": "paper_1752764068738", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764068738_1706.03762v7.pdf", "uploadedAt": "2025-07-17T14:54:28.743Z", "lastAccessedAt": "2025-07-17T14:54:28.743Z", "tags": []}, {"id": "paper_1752764109886", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764109886_2506.21547v1(1).pdf", "uploadedAt": "2025-07-17T14:55:09.901Z", "lastAccessedAt": "2025-07-17T14:55:09.901Z", "tags": []}, {"id": "paper_1752764131170", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764131170_2506.21547v1.pdf", "uploadedAt": "2025-07-17T14:55:31.187Z", "lastAccessedAt": "2025-07-17T14:55:31.187Z", "tags": []}, {"id": "paper_1752764166274", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764166274_2506.21547v1(1).pdf", "uploadedAt": "2025-07-17T14:56:06.290Z", "lastAccessedAt": "2025-07-17T14:56:06.290Z", "tags": []}, {"id": "paper_1752764167388", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 466 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764167388_test-paper.pdf", "uploadedAt": "2025-07-17T14:56:07.389Z", "lastAccessedAt": "2025-07-17T14:56:07.389Z", "tags": []}, {"id": "paper_1752764168880", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 466 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764168880_test-paper.pdf", "uploadedAt": "2025-07-17T14:56:08.881Z", "lastAccessedAt": "2025-07-17T14:56:08.881Z", "tags": []}, {"id": "paper_1752764174690", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 466 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764174690_test-paper.pdf", "uploadedAt": "2025-07-17T14:56:14.691Z", "lastAccessedAt": "2025-07-17T14:56:14.691Z", "tags": []}, {"id": "paper_1752764186735", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764186735_2506.21547v1.pdf", "uploadedAt": "2025-07-17T14:56:26.753Z", "lastAccessedAt": "2025-07-17T14:56:26.753Z", "tags": []}, {"id": "paper_1752764395084", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764395084_1706.03762v7.pdf", "uploadedAt": "2025-07-17T14:59:55.088Z", "lastAccessedAt": "2025-07-17T14:59:55.088Z", "tags": []}, {"id": "paper_1752764472148", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2215244 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764472148_1706.03762v7.pdf", "uploadedAt": "2025-07-17T15:01:12.473Z", "lastAccessedAt": "2025-07-17T15:01:12.473Z", "tags": []}, {"id": "paper_1752764499390", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 2537 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764499390_test-paper.pdf", "uploadedAt": "2025-07-17T15:01:39.393Z", "lastAccessedAt": "2025-07-17T15:01:39.393Z", "tags": []}, {"id": "paper_1752764529162", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764529162_2506.21547v1(1).pdf", "uploadedAt": "2025-07-17T15:02:09.175Z", "lastAccessedAt": "2025-07-17T15:02:09.175Z", "tags": []}, {"id": "paper_1752764951255", "title": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "authors": ["Document Content"], "abstract": "This PDF document has been uploaded and processed. The content extraction is currently using a fallback method.", "content": "Document Content\n\nThis PDF document has been uploaded and processed. The content extraction is currently using a fallback method.\n\nTitle: PDF Document\nPages: 1\nSize: 10997600 bytes\n\nThe document is ready for AI analysis and annotation.", "filePath": "/api/files/1752764951255_2506.21547v1.pdf", "uploadedAt": "2025-07-17T15:09:11.578Z", "lastAccessedAt": "2025-07-17T15:09:11.578Z", "tags": []}]