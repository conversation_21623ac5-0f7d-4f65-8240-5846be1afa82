/* PDF Viewer Styles */
.pdf-viewer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.pdf-viewer-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pdf-document-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-controls {
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid #e1e1e1;
  backdrop-filter: blur(4px);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .pdf-viewer {
    height: calc(100vh - 60px);
  }
  
  .pdf-viewer-wrapper iframe {
    height: 60vh !important;
    min-height: 400px !important;
  }
}

/* Desktop optimizations */
@media (min-width: 769px) {
  .pdf-viewer-wrapper iframe {
    height: 80vh !important;
    min-height: 600px !important;
  }
}

/* <PERSON>m controls styling */
.ant-slider {
  margin: 0 12px;
}

/* Annotation overlay */
.annotation-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.annotation-highlight {
  position: absolute;
  background-color: rgba(255, 255, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.6);
  pointer-events: auto;
  cursor: pointer;
}

.annotation-note {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #1890ff;
  border-radius: 50%;
  pointer-events: auto;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

/* Responsive toolbar */
@media (max-width: 768px) {
  .pdf-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .pdf-toolbar-section {
    width: 100%;
    justify-content: center;
  }
}
